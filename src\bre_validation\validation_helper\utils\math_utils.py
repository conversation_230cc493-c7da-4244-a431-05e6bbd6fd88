"""
Mathematical utility functions for safe operations
"""

from typing import Union, Any


class MathUtils:
    """Utility class for safe mathematical operations"""
    
    @staticmethod
    def safe_abs(value: Any) -> float:
        """
        Absolute value function

        Args:
            value: The value to get absolute value of

        Returns:
            Absolute value as float

        Raises:
            ValueError: If value cannot be converted to float
            TypeError: If value is None or invalid type
        """
        if value is None:
            raise TypeError("Cannot calculate absolute value of None")
        return abs(float(value))
    
    @staticmethod
    def safe_subtract(a: Any, b: Any) -> float:
        """
        Subtraction function

        Args:
            a: First operand
            b: Second operand

        Returns:
            Result of a - b as float

        Raises:
            ValueError: If values cannot be converted to float
            TypeError: If values are None or invalid type
        """
        if a is None or b is None:
            raise TypeError("Cannot subtract None values")
        return float(a) - float(b)
    

