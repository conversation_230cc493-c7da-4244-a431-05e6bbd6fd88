{"dependencies": {"serverless": "3.40.0"}, "scripts": {"deploy:dev": "serverless deploy --stage dev", "package:dev": "serverless package --stage dev", "package:sandbox": "serverless package --stage sandbox", "deploy:sandbox": "serverless deploy --stage sandbox"}, "devDependencies": {"cross-env": "^7.0.3", "serverless-deployment-bucket": "^1.6.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-hooks": "^1.0.0", "serverless-localstack": "^1.3.1", "serverless-plugin-common-excludes": "^4.0.0", "serverless-plugin-include-dependencies": "^6.1.1", "serverless-step-functions": "^3.23.2"}}