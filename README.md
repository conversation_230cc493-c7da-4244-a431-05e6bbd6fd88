# Serverless Project

A serverless application built with the Serverless Framework, featuring step functions, local development support, and comprehensive deployment tooling.

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- AWS CLI configured with appropriate credentials
- Serverless Framework CLI (installed as project dependency)

## Project Structure

```
├── serverless.yml              # Main serverless configuration
├── serverless-config/
│   └── env/
│       └── .env.sample        # Environment variables template
├── src/                       # Source code directory
├── package.json
└── README.md
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp serverless-config/env/.env.sample serverless-config/env/.env
   ```
   Edit the `.env.dev` file with your specific configuration values.

## Available Scripts

### Development Deployment
```bash
npm run deploy:dev
```
Deploys the application to the `dev` stage using environment variables from `.env.sample`.

### Package for Development
```bash
npm run package:dev
```
Packages the application for the `dev` stage without deploying, useful for testing and validation.

## Environment Configuration

Environment variables are managed through the `serverless-dotenv-plugin`. Create environment-specific files:

- `serverless-config/env/.env.dev` - Development environment
- `serverless-config/env/.env.staging` - Staging environment  
- `serverless-config/env/.env.prod` - Production environment

### Required Environment Variables

Copy from `.env.sample` and configure:
```bash
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=your-account-id

# Application Configuration
APP_NAME=your-app-name
STAGE=dev

# Add your specific environment variables here
```

## Deployment

### Development Stage
```bash
npm run deploy:dev
```

### Other Stages
```bash
# Staging
DOTENV_CONFIG_PATH=serverless-config/env/.env.staging npx serverless deploy --stage staging

# Production
DOTENV_CONFIG_PATH=serverless-config/env/.env.prod npx serverless deploy --stage prod
```