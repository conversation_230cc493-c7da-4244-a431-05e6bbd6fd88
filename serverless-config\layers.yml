Boto3Layer:
  name: Boto<PERSON>Layer
  description: This layer contains the Boto3 library.
  compatibleRuntimes:
    - python3.10
    - python3.11
  compatibleArchitectures:
    - x86_64
  package:
    artifact: layers/boto3.zip

PyMongoAWSLayer:
  name: PyMongoAWSLayer
  description: PyMongo library.
  compatibleRuntimes:
    - python3.10
    - python3.11
  compatibleArchitectures:
    - x86_64
  package:
    artifact: layers/pymongoAWS.zip

RequestsLayer:
  name: RequestsLayer
  description: Requests library.
  compatibleRuntimes:
    - python3.10
    - python3.11
  compatibleArchitectures:
    - x86_64
  package:
    artifact: layers/requests.zip

OpenAILayer:
  name: OpenAILayer
  description: OpenAI Python SDK
  compatibleRuntimes:
    - python3.10
    - python3.11
  compatibleArchitectures:
    - x86_64
  package:
    artifact: layers/open_ai.zip