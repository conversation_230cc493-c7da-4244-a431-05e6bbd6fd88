service: hennessy-tag-and-title
useDotenv: true

provider: ${file(./serverless-config/provider/base.yml):provider}

custom: ${file(./serverless-config/custom.yml):custom}

functions:
  - ${file(./serverless-config/resources/functions.yml):functions}

layers: ${file(./serverless-config/layers.yml)}

resources:
  - ${file(./serverless-config/resources/roles/bre_handler_role.yml)}
  - ${file(./serverless-config/resources/roles/llm_extractor.yml)}
  - ${file(./serverless-config/resources/roles/bre_validation_role.yml)}
  - ${file(./serverless-config/resources/roles/bre_post_role.yml)}
  - ${file(./serverless-config/resources/secrets.yml)}

plugins: ${file(./serverless-config/plugins.yml):plugins}

package: ${file(./serverless-config/package.yml):package}