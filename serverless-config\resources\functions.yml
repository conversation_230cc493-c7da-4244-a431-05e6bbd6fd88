functions:
  bre_handler:
    name: ${self:custom.servicePrefix}-bre_handler
    handler: src/bre_handler/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BreHandlerRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_handler/**
        - src/common/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_handler
          method: post

  llm_extractor:
    name: ${self:custom.servicePrefix}-llm_extractor
    handler: src/llm_extractor/lambda_handler.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: LlmExtractorRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: OpenAILayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/llm_extractor/**
        - src/common/**

  bre_validation:
    name: ${self:custom.servicePrefix}-bre_validation
    handler: src/bre_validation/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BreValidationRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_validation/**
        - src/common/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_validation
          method: post

  bre_post:
    name: ${self:custom.servicePrefix}-bre_post
    handler: src/bre_post/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BrePostRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_post/**
        - src/common/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_post
          method: post