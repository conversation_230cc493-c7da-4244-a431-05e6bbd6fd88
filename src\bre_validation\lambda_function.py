import os
import json

from validation_helper.validation_execution import ValidatorExecution
from common.aria_helper.boto3_utils import get_secret, trigger_lambda_response
from common import Mongo

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body['action']
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['status']
        self.mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False)).client
        self.validator_execution = ValidatorExecution()
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({
            "app_id": self.app_id,
            "validation_type_key": "tag_titles"
        })

        if not self.validation_config:
            raise ValueError(f"No validation config found for app_id: {self.app_id}")

    def run(self):
        try:
            # Perform validation
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Build payload for bre_post
            bre_post_payload = {
                "body": json.dumps({
                    "action": self.action_data,
                    "document": self.document,
                    "status": self.statuses,
                    "validation_result": validation_result,
                    "is_valid": is_valid,
                    "bre_type": "validation"
                })
            }

            # Trigger bre_post
            next_function = os.environ.get('BRE_POST_FUNCTION', 'bre_post')
            trigger_lambda_response(next_function, bre_post_payload)

            return {
                "statusCode": 200,
                "body": json.dumps(f"Validation completed and forwarded to {next_function}")
            }

        except Exception as e:
            return {
                "statusCode": 500,
                "body": json.dumps(f"Validation Lambda Error: {str(e)}")
            }


def lambda_handler(event, context):
    print(event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()
