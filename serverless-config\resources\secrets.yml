Resources:
  LLMParamsSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.servicePrefix}-llm_params
      Description: OpenAI credentials and LLM parameters
      SecretString: ${env:LLM_PARAMS}

  MongoUriSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.servicePrefix}-mongodb_uri
      Description: MongoDB connection URI for LLM extractor
      SecretString: ${env:MONGODB_URI}

  AriaCredsSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ${self:custom.servicePrefix}-aria_creds
      Description: MongoDB connection URI for LLM extractor
      SecretString: ${env:ARIA_CREDS}
